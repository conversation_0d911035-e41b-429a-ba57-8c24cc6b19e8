// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {setJSExceptionHandler} from 'react-native-exception-handler';

import {DEFAULT_LOCALE, getTranslations, t} from '@i18n';
import {dismissAllModals, dismissAllOverlays} from '@screens/navigation';
import {isBetaApp} from '@utils/general';
import {
    captureException,
    captureJSException,
    initializeSentry,
} from '@utils/sentry';

import {logWarning} from './log';
import AlertService from './alert_services/alert_services';

class JavascriptAndNativeErrorHandler {
    initializeErrorHandling = () => {
        try {
            initializeSentry();

            // Ensure ErrorUtils is available before proceeding
            if (!global.ErrorUtils) {
                logWarning('ErrorUtils is not available, initializing polyfill');
                global.ErrorUtils = {
                    setGlobalHandler: (handler: (error: any, isFatal: boolean) => void) => {
                        global.__globalErrorHandler = handler;
                    },
                    getGlobalHandler: () => {
                        return global.__globalErrorHandler;
                    },
                    reportError: (error: any) => {
                        if (global.__globalErrorHandler) {
                            global.__globalErrorHandler(error, false);
                        }
                    },
                    reportFatalError: (error: any) => {
                        if (global.__globalErrorHandler) {
                            global.__globalErrorHandler(error, true);
                        }
                    }
                };
            }

            // Check if ErrorUtils is available before setting the handler
            if (global.ErrorUtils && typeof global.ErrorUtils.setGlobalHandler === 'function') {
                try {
                    setJSExceptionHandler(this.errorHandler, false);
                    logWarning('Error handling initialized successfully');
                } catch (error) {
                    logWarning('Failed to set JS exception handler:', error);
                    // Fallback: set the handler directly on ErrorUtils
                    try {
                        global.ErrorUtils.setGlobalHandler(this.errorHandler);
                        logWarning('Fallback error handler set directly on ErrorUtils');
                    } catch (fallbackError) {
                        logWarning('Fallback error handler also failed:', fallbackError);
                    }
                }
            } else {
                logWarning('ErrorUtils setGlobalHandler not available, error handling may not work properly');
                logWarning('ErrorUtils object:', global.ErrorUtils);
            }

            // setNativeExceptionHandler(this.nativeErrorHandler, false);
        } catch (error) {
            logWarning('Failed to initialize error handling:', error);
        }
    };

    nativeErrorHandler = (e: string) => {
        logWarning('Handling native error ' + e);
        captureException(e);
    };

    errorHandler = (e: unknown, isFatal: boolean) => {
        if (__DEV__ && !e && !isFatal) {
            // react-native-exception-handler redirects console.error to call this, and React calls
            // console.error without an exception when prop type validation fails, so this ends up
            // being called with no arguments when the error handler is enabled in dev mode.
            return;
        }

        logWarning('Handling Javascript error', e, isFatal);

        if (isBetaApp || isFatal) {
            captureJSException(e, isFatal);
        }

        if (isFatal && e instanceof Error) {
            const translations = getTranslations(DEFAULT_LOCALE);

            AlertService.alert(
                translations[t('mobile.error_handler.title')],
                translations[t('mobile.error_handler.description')] + `\n\n${e.message}\n\n${e.stack}`,
                [{
                    text: translations[t('mobile.error_handler.button')],
                    onPress: async () => {
                        await dismissAllModals();
                        await dismissAllOverlays();
                    },
                }],
                {cancelable: false},
            );
        }
    };
}

export default new JavascriptAndNativeErrorHandler();
