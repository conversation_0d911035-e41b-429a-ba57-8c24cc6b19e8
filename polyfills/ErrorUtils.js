/**
 * ErrorUtils polyfill for React Native
 * This polyfill ensures that ErrorUtils is available before any other modules are loaded
 * Fixes the "Cannot read property 'setGlobalHandler' of undefined" error
 */

// Initialize ErrorUtils polyfill FIRST before any other imports or code
// This ensures it's available when react-native-exception-handler and Jitsi SDK are imported
if (!global.ErrorUtils) {
    global.ErrorUtils = {
        setGlobalHandler: (handler) => {
            global.__globalErrorHandler = handler;
        },
        getGlobalHandler: () => {
            return global.__globalErrorHandler;
        },
        reportError: (error) => {
            if (global.__globalErrorHandler) {
                global.__globalErrorHandler(error, false);
            }
        },
        reportFatalError: (error) => {
            if (global.__globalErrorHandler) {
                global.__globalErrorHandler(error, true);
            }
        }
    };
}

// Ensure ErrorUtils is properly defined on the global object
if (typeof global.ErrorUtils === 'undefined') {
    console.error('ErrorUtils polyfill failed to initialize properly');
} else {
    console.log('ErrorUtils polyfill initialized successfully');
}
