// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.
/*
const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

const defaultConfig = getDefaultConfig(__dirname);
*/
/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */
/*
module.exports = (async () => {
  const {
    resolver: { sourceExts, assetExts },
  } = await getDefaultConfig();
 
  return {
    transformer: {
      babelTransformerPath: require.resolve('react-native-svg-transformer'),
      getTransformOptions: async () => ({
        transform: {
          experimentalImportSupport: false,
          inlineRequires: false,
        },
      }),
    },
    resolver: {
      assetExts: assetExts.filter(ext => ext !== 'svg'),
      sourceExts: [...sourceExts, 'svg'],
    },
  };
 })();*/

 /**
 * Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * @format
 */

 const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

 const obfuscatorIoMetroPlugin = require('obfuscator-io-metro-plugin')(
  {
    // Obfuscator options (refer to javascript-obfuscator library for more options)
    compact: false,
    sourceMap: false, // Source map generation is disabled
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 1,
    numbersToExpressions: true,
    simplify: true,
    stringArrayShuffle: true,
    splitStrings: true,
    stringArrayThreshold: 1,
  },
  {
    runInDev: false, // Optional: Disable obfuscation in development mode
    logObfuscatedFiles: false, // Optional: Log obfuscated files to ./.jso
  }
 );

 const defaultConfig = getDefaultConfig(__dirname);
 
 const {
   resolver: { sourceExts, assetExts },
 } = getDefaultConfig(__dirname);
 
 const config = {
   transformer: {
     getTransformOptions: async () => ({
       transform: {
         experimentalImportSupport: false,
         inlineRequires: true,
       },
     }),
     babelTransformerPath: require.resolve('react-native-svg-transformer'),
     minifierPath: 'metro-minify-terser',
     minifierConfig: {
       ecma: 8,
       keep_fnames: true,
       mangle: {
         keep_fnames: true,
       },
       compress: {
         drop_console: true,
         drop_debugger: true,
       },
     },
   },
   resolver: {
     assetExts: assetExts.filter(ext => ext !== 'svg'),
     sourceExts: [...sourceExts, 'svg'],
   },
   serializer: {
     getModulesRunBeforeMainModule: () => [
       require.resolve('./polyfills/ErrorUtils.js'),
       require.resolve('react-native/Libraries/Core/InitializeCore'),
     ],
     getPolyfills: () => [],
   },
 };
 
 module.exports = mergeConfig(defaultConfig, config, obfuscatorIoMetroPlugin);
