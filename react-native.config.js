// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

module.exports = {
    dependencies: {
        'react-native-notifications': {
            platforms: {
                android: null,
            },
        },
        'react-native-exception-handler': {
            platforms: {
                android: {
                    sourceDir: '../node_modules/react-native-exception-handler/android',
                    packageImportPath: 'import com.masteratul.exceptionhandler.ReactNativeExceptionHandlerPackage;',
                    packageInstance: 'new ReactNativeExceptionHandlerPackage()'
                },
                ios: {
                    podspecPath: '../node_modules/react-native-exception-handler/ReactNativeExceptionHandler.podspec'
                }
            },
        },
    },
    assets: [
        './assets/fonts',
    ],
};
